# # from pyvim import connect
#
# from pyVmomi import vim
#
# def getCreateContainerView(content, vimtype, name=None):
#     '''
#     列表返回,name 可以指定匹配的对象
#     '''
#     container = content.viewManager.CreateContainerView(content.rootFolder, vimtype, True)
#     obj = [view for view in container.view]
#     return obj
#
#
# def vm_dataCenter(si):
#
#     esxi_host = {}
#
#     content = si.RetrieveContent()
#
#     vm_view = content.viewManager.CreateContainerView(content.rootFolder, [vim.VirtualMachine], True)
#
#     vms = vm_view.view
#
#     # 遍历每个虚拟机，获取其硬件感应器信息
#     for vm in vms:
#         print("虚拟机名称：", vm.name)
#         for device in vm.config.hardware.device:
#             print(vim.vm.device.VirtualSensor)
#     #         if isinstance(device, vim.vm.device.VirtualSensor):
#     #             if device.deviceInfo is not None:
#     #                 version = vim.vm.device.VirtualSensor.GetLatestVersion(device.sensorType)
#     #                 print("感应器名称：", device.deviceInfo.label)
#     #                 print("感应器类型：", device.sensorType)
#     #                 print("感应器版本：", version)
#     # # for esxi in esxi_obj:
#     #     print(esxi.summary)
#     #     print('-------------------------')
#     #     esxi_host[esxi.name] = {'esxi_info': {}, 'datastore': {}, 'network': {}, 'vm': {}}
#     #
#     #     esxi_host[esxi.name]['esxi_info']['廠商'] = esxi.summary.hardware.vendor
#     #     esxi_host[esxi.name]['esxi_info']['型號'] = esxi.summary.hardware.model
#     #
#     #     for i in esxi.summary.hardware.otherIdentifyingInfo:
#     #         if isinstance(i, vim.host.SystemIdentificationInfo):
#     #             esxi_host[esxi.name]['esxi_info']['SN'] = i.identifierValue
#     #     esxi_host[esxi.name]['esxi_info']['處理器'] = '數量：%s 核數：%s 線程數：%s 頻率：%s(%s) ' % (
#     #         esxi.summary.hardware.numCpuPkgs,
#     #         esxi.summary.hardware.numCpuCores,
#     #         esxi.summary.hardware.numCpuThreads,
#     #         esxi.summary.hardware.cpuMhz,
#     #         esxi.summary.hardware.cpuModel)
#     #     esxi_host[esxi.name]['esxi_info']['處理器使用率'] = '%.1f%%' % (esxi.summary.quickStats.overallCpuUsage /
#     #                                                               (
#     #                                                                       esxi.summary.hardware.numCpuPkgs * esxi.summary.hardware.numCpuCores * esxi.summary.hardware.cpuMhz) * 100)
#     #     esxi_host[esxi.name]['esxi_info']['記憶體(MB)'] = esxi.summary.hardware.memorySize / 1024 / 1024
#     #     esxi_host[esxi.name]['esxi_info']['可用記憶體(MB)'] = '%.1f MB' % (
#     #             (esxi.summary.hardware.memorySize / 1024 / 1024) - esxi.summary.quickStats.overallMemoryUsage)
#     #     esxi_host[esxi.name]['esxi_info']['記憶體使用率'] = '%.1f%%' % ((esxi.summary.quickStats.overallMemoryUsage / (
#     #             esxi.summary.hardware.memorySize / 1024 / 1024)) * 100)
#     #     esxi_host[esxi.name]['esxi_info']['系統'] = esxi.summary.config.product.fullName
#     #
#     #     for ds in esxi.datastore:
#     #         esxi_host[esxi.name]['datastore'][ds.name] = {}
#     #         esxi_host[esxi.name]['datastore'][ds.name]['總容量(G)'] = int((ds.summary.capacity) / 1024 / 1024 / 1024)
#     #         esxi_host[esxi.name]['datastore'][ds.name]['剩餘容量(G)'] = int((ds.summary.freeSpace) / 1024 / 1024 / 1024)
#     #         esxi_host[esxi.name]['datastore'][ds.name]['類型'] = (ds.summary.type)
#     #     for nt in esxi.network:
#     #         esxi_host[esxi.name]['network'][nt.name] = {}
#     #         esxi_host[esxi.name]['network'][nt.name]['標籤ID'] = nt.name
#     #     for vm in esxi.vm:
#     #         esxi_host[esxi.name]['vm'][vm.name] = {}
#     #         esxi_host[esxi.name]['vm'][vm.name]['電源狀態'] = vm.runtime.powerState
#     #         esxi_host[esxi.name]['vm'][vm.name]['CPU(內核總數)'] = vm.config.hardware.numCPU
#     #         esxi_host[esxi.name]['vm'][vm.name]['記憶體(總數MB)'] = vm.config.hardware.memoryMB
#     #         esxi_host[esxi.name]['vm'][vm.name]['系統資訊'] = vm.config.guestFullName
#     #         if vm.guest.ipAddress:
#     #             esxi_host[esxi.name]['vm'][vm.name]['IP'] = vm.guest.ipAddress
#     #         else:
#     #             esxi_host[esxi.name]['vm'][vm.name]['IP'] = '服務器需要重開機才能讀取'
#     #
#     #         for d in vm.config.hardware.device:
#     #             if isinstance(d, vim.vm.device.VirtualDisk):
#     #                 esxi_host[esxi.name]['vm'][vm.name][d.deviceInfo.label] = str(
#     #                     (d.capacityInKB) / 1024 / 1024) + ' GB'
#     #
#     # f = open('aa' + '.txt', 'w')
#     # for host in esxi_host:
#     #     print('ESXI IP:', host)
#     #     f.write('ESXI IP: %s \n' % host)
#     #     for hd in esxi_host[host]['esxi_info']:
#     #         print('  %s:    %s' % (hd, esxi_host[host]['esxi_info'][hd]))
#     #         f.write('  %s:    %s' % (hd, esxi_host[host]['esxi_info'][hd]))
#     #     for ds in esxi_host[host]['datastore']:
#     #         print('  儲存名稱：', ds)
#     #         f.write('  儲存名稱： %s \n' % ds)
#     #         for k in esxi_host[host]['datastore'][ds]:
#     #             print('       %s:  %s' % (k, esxi_host[host]['datastore'][ds][k]))
#     #             f.write('       %s:  %s \n' % (k, esxi_host[host]['datastore'][ds][k]))
#     #     for nt in esxi_host[host]['network']:
#     #         print('  網路名稱：', nt)
#     #         f.write('  網路名稱：%s \n' % nt)
#     #         for k in esxi_host[host]['network'][nt]:
#     #             print('        %s:  %s' % (k, esxi_host[host]['network'][nt][k]))
#     #             f.write('        %s:  %s \n' % (k, esxi_host[host]['network'][nt][k]))
#     #     for vmachine in esxi_host[host]['vm']:
#     #         print('  虛擬機名稱：', vmachine)
#     #         f.write('  虛擬機名稱：%s \n' % vmachine)
#     #         for k in esxi_host[host]['vm'][vmachine]:
#     #             print('        %s:  %s' % (k, esxi_host[host]['vm'][vmachine][k]))
#     #             f.write('        %s:  %s \n' % (k, esxi_host[host]['vm'][vmachine][k]))
#     # f.close()