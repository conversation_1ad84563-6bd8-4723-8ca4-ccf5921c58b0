import datetime
import json

import json as JSON

import requests as requests

from ComputerHardware import WindowsListen

# import ssl

from apis.api import CreateComputerInfo, CreateDiskInfo, CreateDiskInfoWindowsEventLog
from apis.constant import http_https, localhost, localport
from tools import service_instance, cli
from vmDataCenterHardware import vm_dataCenter

# ssl._create_default_https_context = ssl._create_unverified_context

from pyVmomi import vmodl

servers = [
    # ('**************','',''),

    ('HSWWEB00', 'administrator', 'WEBsystem101'),

]

servers2 = [
    # ('*************', '16613', 'Tt9566659', 443),
    # ('*************', '<EMAIL>', 'AD)&)VC%%', 443),
    # ('*************', '<EMAIL>', '`1qaz~!QAZ', 443),
]


def write_to_log_file(file_path, text):
    with open(file_path, 'a', encoding='utf-8') as log_file:
        log_file.write(text + '\n')


if __name__ == '__main__':

    WinDis = WindowsListen()

    log_file_path = 'log.txt'

    for server, username, password in servers:
        # 電腦資訊
        DataList = JSON.loads(WinDis.getComputerSystemInfo(server, username, password))
        # print(json.dumps(DataList, ensure_ascii=False))
        write_to_log_file(log_file_path, json.dumps(DataList, ensure_ascii=False))

        url = http_https() + '://' + localhost() + ':' + localport() + CreateComputerInfo()
        headers = {'Content-type': 'application/json'}
        x = requests.post(url, data=json.dumps(DataList), headers=headers)
        # print(x)

        # # 硬碟資訊
        DataList = JSON.loads(WinDis.getDiskInfo(server, username, password))
        # print(json.dumps(DataList, ensure_ascii=False))

        url = http_https() + '://' + localhost() + ':' + localport() + CreateDiskInfo()
        headers = {'Content-type': 'application/json'}
        x = requests.post(url, data=json.dumps(DataList), headers=headers)

        # # 電腦日誌
        DataList = JSON.loads(WinDis.getWindowsEventLog(server, username, password))
        # print(json.dumps(DataList, ensure_ascii=False))

        url = http_https() + '://' + localhost() + ':' + localport() + CreateDiskInfoWindowsEventLog()
        headers = {'Content-type': 'application/json'}
        x = requests.post(url, data=json.dumps(DataList), headers=headers)

    # for server, username, password, port in servers2:
    #     try:
    #         si = service_instance.connect(server, username, password, port)
    #
    #         # Do the actual parsing of data
    #         vm_dataCenter(si)
    #
    #     except vmodl.MethodFault as ex:
    #         print("Caught vmodl fault : {}".format(ex.msg))
