#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
替代的事件日誌查詢方法
針對 HSWDC00 等老舊系統的效能最佳化
"""

import wmi
import json
import time
from datetime import datetime, timedelta, timezone
from setting import ip_to_computer_name_map_2

class OptimizedEventLogQuery:
    """最佳化的事件日誌查詢類別"""
    
    def __init__(self):
        self.max_records = 50  # 進一步減少記錄數
        self.query_timeout = 60  # 查詢逾時設定
        
    def getWindowsEventLogFast(self, ip, user, password):
        """快速版本的事件日誌查詢"""
        strIP, strUser, strPassword = ip, user, password
        
        # 初始化返回JSON格式
        Ret = {"Code": "0", "Msg": "OK", "Data": []}
        
        print(f"開始快速事件日誌查詢: {strIP}")
        
        # 連線服務器
        try:
            start_time = time.time()
            getData = wmi.WMI(computer=strIP, user=strUser, password=strPassword)
            connection_time = time.time() - start_time
            print(f"WMI 連線成功，耗時: {connection_time:.2f}秒")
        except Exception as e:
            Ret["Code"] = "-1"
            Ret["Msg"] = f"服務器 : {strIP} 連接失敗！{str(e)}"
            return json.dumps(Ret, ensure_ascii=False)
        
        # 設定更短的時間範圍 - 只查詢最近6小時
        end_date = datetime.now()
        start_date = end_date - timedelta(hours=6)
        
        print(f"查詢時間範圍: {start_date} 到 {end_date}")
        
        # 分別查詢不同類型的事件，避免大型查詢
        event_types = ['錯誤', '重大']  # 先只查詢最重要的
        all_events = []
        
        for event_type in event_types:
            try:
                print(f"查詢 {event_type} 類型事件...")
                
                # 簡化的查詢語句
                query = f"""SELECT TOP {self.max_records//len(event_types)} 
                           TimeGenerated, ComputerName, Type, Logfile, Message 
                           FROM Win32_NTLogEvent 
                           WHERE Type = '{event_type}' 
                           AND TimeGenerated >= '{start_date.strftime('%Y%m%d%H%M%S.000000-000')}'
                           ORDER BY TimeGenerated DESC"""
                
                query_start = time.time()
                events = getData.query(query)
                query_time = time.time() - query_start
                
                print(f"{event_type} 查詢完成，耗時: {query_time:.2f}秒")
                
                # 處理查詢結果
                for event in events:
                    try:
                        all_events.append(event)
                        if len(all_events) >= self.max_records:
                            break
                    except Exception as e:
                        print(f"處理事件時出錯: {e}")
                        continue
                        
                if len(all_events) >= self.max_records:
                    break
                    
            except Exception as e:
                print(f"查詢 {event_type} 事件時出錯: {e}")
                continue
        
        # 處理收集到的事件
        Data = []
        computer_name = ''
        
        print(f"開始處理 {len(all_events)} 個事件...")
        
        for i, event in enumerate(all_events):
            try:
                # 安全地獲取時間
                time_str = getattr(event, 'TimeGenerated', '')
                if len(time_str) >= 14:
                    dt = datetime(
                        int(time_str[0:4]), int(time_str[4:6]), int(time_str[6:8]),
                        int(time_str[8:10]), int(time_str[10:12]), int(time_str[12:14]),
                        tzinfo=timezone.utc
                    )
                    local_dt = dt.astimezone()
                    date = local_dt.strftime('%Y%m%d')
                    time_formatted = local_dt.strftime('%H%M%S')
                else:
                    continue
                
                # 獲取電腦名稱
                if hasattr(event, 'ComputerName') and event.ComputerName:
                    computer_name = event.ComputerName
                
                # 安全地獲取訊息
                message = "無訊息"
                if hasattr(event, 'Message') and event.Message:
                    message = str(event.Message)[:100]  # 進一步縮短訊息長度
                    message = message.replace("'", "").replace('"', '')
                
                log_entry = {
                    "ComputerName": computer_name or ip_to_computer_name_map_2.get(strIP, f"{strIP}.heysong.com.tw"),
                    "TimeGenerated_Date": f"{int(date[:4]) - 1911}{date[4:]}",
                    "TimeGenerated_Time": time_formatted,
                    "Type": getattr(event, 'Type', '未知'),
                    "Logfile": getattr(event, 'Logfile', '未知'),
                    "Message": message
                }
                
                Data.append(log_entry)
                
            except Exception as e:
                print(f"處理第 {i+1} 個事件時出錯: {e}")
                continue
        
        # 如果沒有找到任何事件，添加一個預設記錄
        if not Data:
            computer_name = computer_name or ip_to_computer_name_map_2.get(strIP, f"{strIP}.heysong.com.tw")
            today = datetime.now().strftime('%Y%m%d')
            default_log = {
                "ComputerName": computer_name,
                "TimeGenerated_Date": f"{int(today[:4]) - 1911}{today[4:]}",
                "TimeGenerated_Time": "000000",
                "Type": "信息",
                "Logfile": "Application",
                "Message": "查詢期間無重大錯誤事件"
            }
            Data.append(default_log)
        
        # 按時間排序
        Data.sort(key=lambda x: (x["TimeGenerated_Date"], x["TimeGenerated_Time"]), reverse=True)
        
        Ret["Data"] = Data
        print(f"快速查詢完成，返回 {len(Data)} 筆記錄")
        
        return json.dumps(Ret, ensure_ascii=False)

# 測試函數
def test_fast_query():
    """測試快速查詢功能"""
    from setting import servers
    
    if not servers:
        print("沒有找到伺服器配置")
        return
    
    optimizer = OptimizedEventLogQuery()
    
    for ip, username, password in servers:
        print(f"\n{'='*60}")
        print(f"測試伺服器: {ip}")
        print(f"{'='*60}")
        
        start_time = time.time()
        result = optimizer.getWindowsEventLogFast(ip, username, password)
        total_time = time.time() - start_time
        
        data = json.loads(result)
        
        print(f"\n結果:")
        print(f"狀態碼: {data['Code']}")
        print(f"訊息: {data['Msg']}")
        print(f"記錄數: {len(data['Data'])}")
        print(f"總耗時: {total_time:.2f}秒")
        
        if data['Code'] == '0' and data['Data']:
            print(f"\n最新3筆記錄:")
            for i, log in enumerate(data['Data'][:3], 1):
                print(f"  {i}. [{log['TimeGenerated_Date']} {log['TimeGenerated_Time']}] "
                      f"{log['Type']} - {log['Message'][:50]}...")

if __name__ == "__main__":
    test_fast_query()
