2025-07-25 10:27:59 - INFO - [getComputerSystemInfo:211] - ============================================================
2025-07-25 10:27:59 - INFO - [getComputerSystemInfo:212] - 開始獲取電腦系統資訊: 172.20.160.35
2025-07-25 10:27:59 - INFO - [getComputerSystemInfo:213] - 用戶名: HSWBS01\PowerUser
2025-07-25 10:27:59 - INFO - [getComputerSystemInfo:214] - ============================================================
2025-07-25 10:27:59 - INFO - [getComputerSystemInfo:220] - 步驟 1: 檢查網路連通性
2025-07-25 10:27:59 - INFO - [check_network_connectivity:66] - 開始檢查網路連通性: 172.20.160.35
2025-07-25 10:27:59 - ERROR - [check_network_connectivity:86] - Ping 測試異常: 172.20.160.35 - __init__() got an unexpected keyword argument 'capture_output'
2025-07-25 10:27:59 - INFO - [check_network_connectivity:97] - TCP 135 端口測試 172.20.160.35: 成功
2025-07-25 10:27:59 - ERROR - [getComputerSystemInfo:225] - 網路連通性檢查失敗 - 無法 ping 通 172.20.160.35
2025-07-25 10:27:59 - DEBUG - [_new_conn:1007] - Starting new HTTPS connection (1): api.heysong.dev:443
2025-07-25 10:28:21 - INFO - [getComputerSystemInfo:211] - ============================================================
2025-07-25 10:28:21 - INFO - [getComputerSystemInfo:212] - 開始獲取電腦系統資訊: 172.20.160.132
2025-07-25 10:28:21 - INFO - [getComputerSystemInfo:213] - 用戶名: HSWORA02\PowerUser
2025-07-25 10:28:21 - INFO - [getComputerSystemInfo:214] - ============================================================
2025-07-25 10:28:21 - INFO - [getComputerSystemInfo:220] - 步驟 1: 檢查網路連通性
2025-07-25 10:28:21 - INFO - [check_network_connectivity:66] - 開始檢查網路連通性: 172.20.160.132
2025-07-25 10:28:21 - ERROR - [check_network_connectivity:86] - Ping 測試異常: 172.20.160.132 - __init__() got an unexpected keyword argument 'capture_output'
2025-07-25 10:28:21 - INFO - [check_network_connectivity:97] - TCP 135 端口測試 172.20.160.132: 成功
2025-07-25 10:28:21 - ERROR - [getComputerSystemInfo:225] - 網路連通性檢查失敗 - 無法 ping 通 172.20.160.132
2025-07-25 10:28:21 - DEBUG - [_new_conn:1007] - Starting new HTTPS connection (1): api.heysong.dev:443
2025-07-25 10:28:49 - DEBUG - [_make_request:465] - https://api.heysong.dev:443 "POST /api/main_se/CreateComputerInfo/ HTTP/1.1" 200 63
2025-07-25 10:28:49 - INFO - [getDiskInfo:732] - ============================================================
2025-07-25 10:28:49 - INFO - [getDiskInfo:733] - 開始獲取硬碟資訊: 172.20.160.132
2025-07-25 10:28:49 - INFO - [getDiskInfo:734] - 用戶名: HSWORA02\PowerUser
2025-07-25 10:28:49 - INFO - [getDiskInfo:735] - ============================================================
2025-07-25 10:28:49 - INFO - [getDiskInfo:738] - 步驟 1: 檢查網路連通性
2025-07-25 10:28:49 - INFO - [check_network_connectivity:66] - 開始檢查網路連通性: 172.20.160.132
2025-07-25 10:28:49 - ERROR - [check_network_connectivity:86] - Ping 測試異常: 172.20.160.132 - __init__() got an unexpected keyword argument 'capture_output'
2025-07-25 10:28:49 - INFO - [check_network_connectivity:97] - TCP 135 端口測試 172.20.160.132: 成功
2025-07-25 10:28:49 - ERROR - [getDiskInfo:743] - 網路連通性檢查失敗 - 無法 ping 通 172.20.160.132
2025-07-25 10:28:49 - INFO - [getWindowsEventLog:923] - ============================================================
2025-07-25 10:28:49 - INFO - [getWindowsEventLog:924] - 開始獲取 Windows 事件日誌: 172.20.160.132
2025-07-25 10:28:49 - INFO - [getWindowsEventLog:925] - 用戶名: HSWORA02\PowerUser
2025-07-25 10:28:49 - INFO - [getWindowsEventLog:926] - ============================================================
2025-07-25 10:28:49 - INFO - [getWindowsEventLog:932] - 步驟 1: 檢查網路連通性
2025-07-25 10:28:49 - INFO - [check_network_connectivity:66] - 開始檢查網路連通性: 172.20.160.132
2025-07-25 10:28:49 - ERROR - [check_network_connectivity:86] - Ping 測試異常: 172.20.160.132 - __init__() got an unexpected keyword argument 'capture_output'
2025-07-25 10:28:49 - INFO - [check_network_connectivity:97] - TCP 135 端口測試 172.20.160.132: 成功
2025-07-25 10:28:49 - ERROR - [getWindowsEventLog:937] - 網路連通性檢查失敗 - 無法 ping 通 172.20.160.132
2025-07-25 10:31:51 - INFO - [getComputerSystemInfo:183] - ============================================================
2025-07-25 10:31:51 - INFO - [getComputerSystemInfo:184] - 開始獲取電腦系統資訊: 172.20.160.132
2025-07-25 10:31:51 - INFO - [getComputerSystemInfo:185] - 用戶名: HSWORA02\PowerUser
2025-07-25 10:31:51 - INFO - [getComputerSystemInfo:186] - ============================================================
2025-07-25 10:31:51 - INFO - [getComputerSystemInfo:192] - 步驟 1: 建立 WMI 連接並測試
2025-07-25 10:31:51 - INFO - [test_wmi_connectivity:66] - 開始測試 WMI 連接: 172.20.160.132 (用戶: HSWORA02\PowerUser)
2025-07-25 10:31:51 - INFO - [test_wmi_connectivity:83] - 嘗試建立 WMI 連接: 172.20.160.132
2025-07-25 10:32:09 - INFO - [test_wmi_connectivity:89] - WMI 連接成功: 172.20.160.132 (耗時: 18.26秒)
2025-07-25 10:32:09 - INFO - [test_wmi_connectivity:94] - WMI 查詢測試成功: 172.20.160.132
2025-07-25 10:32:09 - INFO - [getComputerSystemInfo:202] - WMI 連接建立成功: 172.20.160.132
2025-07-25 10:32:09 - INFO - [getComputerSystemInfo:205] - 步驟 3: 查詢作業系統資訊
2025-07-25 10:32:09 - INFO - [getComputerSystemInfo:212] - Win32_OperatingSystem 查詢完成 (耗時: 0.04秒)
2025-07-25 10:32:09 - INFO - [getComputerSystemInfo:220] - 找到 1 個作業系統記錄
2025-07-25 10:32:09 - INFO - [getComputerSystemInfo:230] - 處理第 1 個作業系統記錄
2025-07-25 10:32:09 - DEBUG - [getComputerSystemInfo:236] - 原始數據 - CSName: HSWORA02
2025-07-25 10:32:09 - DEBUG - [getComputerSystemInfo:237] - 原始數據 - Caption: Microsoft Windows Server 2012 R2 Standard
2025-07-25 10:32:09 - DEBUG - [getComputerSystemInfo:238] - 原始數據 - BuildNumber: 9600
2025-07-25 10:32:09 - DEBUG - [getComputerSystemInfo:239] - 原始數據 - TotalVisibleMemorySize: 33553972
2025-07-25 10:32:09 - DEBUG - [getComputerSystemInfo:240] - 原始數據 - FreePhysicalMemory: 5893048
2025-07-25 10:32:09 - INFO - [getComputerSystemInfo:266] - 記憶體資訊處理完成 - 總計: 32.00GB, 已使用: 26.38GB, 使用率: 82.44%
2025-07-25 10:32:09 - INFO - [getComputerSystemInfo:276] - 步驟 4: 資料處理完成
2025-07-25 10:32:09 - INFO - [getComputerSystemInfo:277] - 成功處理 1 筆系統資訊
2025-07-25 10:32:09 - INFO - [getComputerSystemInfo:281] - getComputerSystemInfo 完成: 172.20.160.132
2025-07-25 10:32:09 - INFO - [getComputerSystemInfo:282] - ============================================================
2025-07-25 10:32:09 - DEBUG - [_new_conn:1007] - Starting new HTTPS connection (1): api.heysong.dev:443
2025-07-25 10:32:33 - DEBUG - [_make_request:465] - https://api.heysong.dev:443 "POST /api/main_se/CreateComputerInfo/ HTTP/1.1" 200 63
2025-07-25 10:32:33 - INFO - [getDiskInfo:690] - ============================================================
2025-07-25 10:32:33 - INFO - [getDiskInfo:691] - 開始獲取硬碟資訊: 172.20.160.132
2025-07-25 10:32:33 - INFO - [getDiskInfo:692] - 用戶名: HSWORA02\PowerUser
2025-07-25 10:32:33 - INFO - [getDiskInfo:693] - ============================================================
2025-07-25 10:32:33 - INFO - [getDiskInfo:696] - 步驟 1: 建立 CIMv2 WMI 連接
2025-07-25 10:32:33 - INFO - [getDiskInfo:701] - CIMv2 WMI 連接成功 (耗時: 0.02秒)
2025-07-25 10:32:33 - INFO - [getDiskInfo:704] - 步驟 2: 嘗試連接 Storage namespace
2025-07-25 10:32:33 - WARNING - [getDiskInfo:711] - Storage namespace 不可用 (舊系統): <x_wmi: Unexpected COM Error (-2147024891, '存取被拒。', None, None)>
2025-07-25 10:32:34 - DEBUG - [_new_conn:1007] - Starting new HTTPS connection (1): api.heysong.dev:443
2025-07-25 10:32:55 - DEBUG - [_make_request:465] - https://api.heysong.dev:443 "POST /api/main_se/GetDiskInfo/ HTTP/1.1" 200 946
2025-07-25 10:32:55 - DEBUG - [_new_conn:1007] - Starting new HTTPS connection (1): api.heysong.dev:443
2025-07-25 10:33:16 - DEBUG - [_make_request:465] - https://api.heysong.dev:443 "POST /api/main_se/CreateDiskInfo/ HTTP/1.1" 200 63
2025-07-25 10:33:16 - INFO - [getWindowsEventLog:882] - ============================================================
2025-07-25 10:33:16 - INFO - [getWindowsEventLog:883] - 開始獲取 Windows 事件日誌: 172.20.160.132
2025-07-25 10:33:16 - INFO - [getWindowsEventLog:884] - 用戶名: HSWORA02\PowerUser
2025-07-25 10:33:16 - INFO - [getWindowsEventLog:885] - ============================================================
2025-07-25 10:33:16 - INFO - [getWindowsEventLog:891] - 步驟 1: 建立 WMI 連接
2025-07-25 10:33:16 - ERROR - [getWindowsEventLog:900] - 服務器 : 172.20.160.132 連接失敗！local variable 'time' referenced before assignment (耗時: 0.00秒)
2025-07-25 10:33:16 - INFO - [sendDiskSpaceAlert:627] - 已成功發送硬碟空間報告郵件給 <EMAIL>, <EMAIL>
2025-07-25 10:33:28 - INFO - [getComputerSystemInfo:183] - ============================================================
2025-07-25 10:33:28 - INFO - [getComputerSystemInfo:184] - 開始獲取電腦系統資訊: 172.20.160.33
2025-07-25 10:33:28 - INFO - [getComputerSystemInfo:185] - 用戶名: HSWBI00\PowerUser
2025-07-25 10:33:28 - INFO - [getComputerSystemInfo:186] - ============================================================
2025-07-25 10:33:28 - INFO - [getComputerSystemInfo:192] - 步驟 1: 建立 WMI 連接並測試
2025-07-25 10:33:28 - INFO - [test_wmi_connectivity:66] - 開始測試 WMI 連接: 172.20.160.33 (用戶: HSWBI00\PowerUser)
2025-07-25 10:33:28 - INFO - [test_wmi_connectivity:83] - 嘗試建立 WMI 連接: 172.20.160.33
2025-07-25 10:33:46 - INFO - [test_wmi_connectivity:89] - WMI 連接成功: 172.20.160.33 (耗時: 18.31秒)
2025-07-25 10:33:46 - INFO - [test_wmi_connectivity:94] - WMI 查詢測試成功: 172.20.160.33
2025-07-25 10:33:46 - INFO - [getComputerSystemInfo:202] - WMI 連接建立成功: 172.20.160.33
2025-07-25 10:33:46 - INFO - [getComputerSystemInfo:205] - 步驟 3: 查詢作業系統資訊
2025-07-25 10:33:46 - INFO - [getComputerSystemInfo:212] - Win32_OperatingSystem 查詢完成 (耗時: 0.06秒)
2025-07-25 10:33:46 - INFO - [getComputerSystemInfo:220] - 找到 1 個作業系統記錄
2025-07-25 10:33:46 - INFO - [getComputerSystemInfo:230] - 處理第 1 個作業系統記錄
2025-07-25 10:33:46 - DEBUG - [getComputerSystemInfo:236] - 原始數據 - CSName: HSWBI00
2025-07-25 10:33:46 - DEBUG - [getComputerSystemInfo:237] - 原始數據 - Caption: Microsoft Windows Server 2008 R2 Standard 
2025-07-25 10:33:46 - DEBUG - [getComputerSystemInfo:238] - 原始數據 - BuildNumber: 7601
2025-07-25 10:33:46 - DEBUG - [getComputerSystemInfo:239] - 原始數據 - TotalVisibleMemorySize: 25165368
2025-07-25 10:33:46 - DEBUG - [getComputerSystemInfo:240] - 原始數據 - FreePhysicalMemory: 13339656
2025-07-25 10:33:46 - INFO - [getComputerSystemInfo:266] - 記憶體資訊處理完成 - 總計: 24.00GB, 已使用: 11.28GB, 使用率: 46.99%
2025-07-25 10:33:46 - INFO - [getComputerSystemInfo:276] - 步驟 4: 資料處理完成
2025-07-25 10:33:46 - INFO - [getComputerSystemInfo:277] - 成功處理 1 筆系統資訊
2025-07-25 10:33:46 - INFO - [getComputerSystemInfo:281] - getComputerSystemInfo 完成: 172.20.160.33
2025-07-25 10:33:46 - INFO - [getComputerSystemInfo:282] - ============================================================
2025-07-25 10:33:46 - DEBUG - [_new_conn:1007] - Starting new HTTPS connection (1): api.heysong.dev:443
2025-07-25 10:33:47 - DEBUG - [_make_request:465] - https://api.heysong.dev:443 "POST /api/main_se/CreateComputerInfo/ HTTP/1.1" 200 63
2025-07-25 10:33:47 - INFO - [getDiskInfo:690] - ============================================================
2025-07-25 10:33:47 - INFO - [getDiskInfo:691] - 開始獲取硬碟資訊: 172.20.160.33
2025-07-25 10:33:47 - INFO - [getDiskInfo:692] - 用戶名: HSWBI00\PowerUser
2025-07-25 10:33:47 - INFO - [getDiskInfo:693] - ============================================================
2025-07-25 10:33:47 - INFO - [getDiskInfo:696] - 步驟 1: 建立 CIMv2 WMI 連接
2025-07-25 10:33:47 - INFO - [getDiskInfo:701] - CIMv2 WMI 連接成功 (耗時: 0.02秒)
2025-07-25 10:33:47 - INFO - [getDiskInfo:704] - 步驟 2: 嘗試連接 Storage namespace
2025-07-25 10:33:47 - WARNING - [getDiskInfo:711] - Storage namespace 不可用 (舊系統): <x_wmi: Unexpected COM Error (-2147024891, '存取被拒。', None, None)>
2025-07-25 10:33:47 - DEBUG - [_new_conn:1007] - Starting new HTTPS connection (1): api.heysong.dev:443
2025-07-25 10:33:47 - DEBUG - [_make_request:465] - https://api.heysong.dev:443 "POST /api/main_se/GetDiskInfo/ HTTP/1.1" 200 706
2025-07-25 10:33:47 - DEBUG - [_new_conn:1007] - Starting new HTTPS connection (1): api.heysong.dev:443
2025-07-25 10:33:47 - DEBUG - [_make_request:465] - https://api.heysong.dev:443 "POST /api/main_se/CreateDiskInfo/ HTTP/1.1" 200 63
2025-07-25 10:33:47 - INFO - [getWindowsEventLog:882] - ============================================================
2025-07-25 10:33:47 - INFO - [getWindowsEventLog:883] - 開始獲取 Windows 事件日誌: 172.20.160.33
2025-07-25 10:33:47 - INFO - [getWindowsEventLog:884] - 用戶名: HSWBI00\PowerUser
2025-07-25 10:33:47 - INFO - [getWindowsEventLog:885] - ============================================================
2025-07-25 10:33:47 - INFO - [getWindowsEventLog:891] - 步驟 1: 建立 WMI 連接
2025-07-25 10:33:47 - ERROR - [getWindowsEventLog:900] - 服務器 : 172.20.160.33 連接失敗！local variable 'time' referenced before assignment (耗時: 0.00秒)
2025-07-25 10:33:47 - INFO - [sendDiskSpaceAlert:627] - 已成功發送硬碟空間報告郵件給 <EMAIL>, <EMAIL>
2025-07-25 10:34:58 - INFO - [getComputerSystemInfo:183] - ============================================================
2025-07-25 10:34:58 - INFO - [getComputerSystemInfo:184] - 開始獲取電腦系統資訊: 172.20.160.33
2025-07-25 10:34:58 - INFO - [getComputerSystemInfo:185] - 用戶名: HSWBI00\PowerUser
2025-07-25 10:34:58 - INFO - [getComputerSystemInfo:186] - ============================================================
2025-07-25 10:34:58 - INFO - [getComputerSystemInfo:192] - 步驟 1: 建立 WMI 連接並測試
2025-07-25 10:34:58 - INFO - [test_wmi_connectivity:66] - 開始測試 WMI 連接: 172.20.160.33 (用戶: HSWBI00\PowerUser)
2025-07-25 10:34:58 - INFO - [test_wmi_connectivity:83] - 嘗試建立 WMI 連接: 172.20.160.33
2025-07-25 10:35:16 - INFO - [test_wmi_connectivity:89] - WMI 連接成功: 172.20.160.33 (耗時: 18.25秒)
2025-07-25 10:35:16 - INFO - [test_wmi_connectivity:94] - WMI 查詢測試成功: 172.20.160.33
2025-07-25 10:35:16 - INFO - [getComputerSystemInfo:202] - WMI 連接建立成功: 172.20.160.33
2025-07-25 10:35:16 - INFO - [getComputerSystemInfo:205] - 步驟 3: 查詢作業系統資訊
2025-07-25 10:35:16 - INFO - [getComputerSystemInfo:212] - Win32_OperatingSystem 查詢完成 (耗時: 0.06秒)
2025-07-25 10:35:16 - INFO - [getComputerSystemInfo:220] - 找到 1 個作業系統記錄
2025-07-25 10:35:16 - INFO - [getComputerSystemInfo:230] - 處理第 1 個作業系統記錄
2025-07-25 10:35:16 - DEBUG - [getComputerSystemInfo:236] - 原始數據 - CSName: HSWBI00
2025-07-25 10:35:16 - DEBUG - [getComputerSystemInfo:237] - 原始數據 - Caption: Microsoft Windows Server 2008 R2 Standard 
2025-07-25 10:35:16 - DEBUG - [getComputerSystemInfo:238] - 原始數據 - BuildNumber: 7601
2025-07-25 10:35:16 - DEBUG - [getComputerSystemInfo:239] - 原始數據 - TotalVisibleMemorySize: 25165368
2025-07-25 10:35:16 - DEBUG - [getComputerSystemInfo:240] - 原始數據 - FreePhysicalMemory: 13333916
2025-07-25 10:35:16 - INFO - [getComputerSystemInfo:266] - 記憶體資訊處理完成 - 總計: 24.00GB, 已使用: 11.28GB, 使用率: 47.01%
2025-07-25 10:35:16 - INFO - [getComputerSystemInfo:276] - 步驟 4: 資料處理完成
2025-07-25 10:35:16 - INFO - [getComputerSystemInfo:277] - 成功處理 1 筆系統資訊
2025-07-25 10:35:16 - INFO - [getComputerSystemInfo:281] - getComputerSystemInfo 完成: 172.20.160.33
2025-07-25 10:35:16 - INFO - [getComputerSystemInfo:282] - ============================================================
2025-07-25 10:35:16 - DEBUG - [_new_conn:1007] - Starting new HTTPS connection (1): api.heysong.dev:443
2025-07-25 10:35:17 - DEBUG - [_make_request:465] - https://api.heysong.dev:443 "POST /api/main_se/CreateComputerInfo/ HTTP/1.1" 200 63
2025-07-25 10:35:17 - INFO - [getDiskInfo:690] - ============================================================
2025-07-25 10:35:17 - INFO - [getDiskInfo:691] - 開始獲取硬碟資訊: 172.20.160.33
2025-07-25 10:35:17 - INFO - [getDiskInfo:692] - 用戶名: HSWBI00\PowerUser
2025-07-25 10:35:17 - INFO - [getDiskInfo:693] - ============================================================
2025-07-25 10:35:17 - INFO - [getDiskInfo:696] - 步驟 1: 建立 CIMv2 WMI 連接
2025-07-25 10:35:17 - INFO - [getDiskInfo:701] - CIMv2 WMI 連接成功 (耗時: 0.01秒)
2025-07-25 10:35:17 - INFO - [getDiskInfo:704] - 步驟 2: 嘗試連接 Storage namespace
2025-07-25 10:35:17 - WARNING - [getDiskInfo:711] - Storage namespace 不可用 (舊系統): <x_wmi: Unexpected COM Error (-2147024891, '存取被拒。', None, None)>
2025-07-25 10:35:17 - DEBUG - [_new_conn:1007] - Starting new HTTPS connection (1): api.heysong.dev:443
2025-07-25 10:35:17 - DEBUG - [_make_request:465] - https://api.heysong.dev:443 "POST /api/main_se/GetDiskInfo/ HTTP/1.1" 200 706
2025-07-25 10:35:17 - DEBUG - [_new_conn:1007] - Starting new HTTPS connection (1): api.heysong.dev:443
2025-07-25 10:35:17 - DEBUG - [_make_request:465] - https://api.heysong.dev:443 "POST /api/main_se/CreateDiskInfo/ HTTP/1.1" 200 63
2025-07-25 10:35:17 - INFO - [getWindowsEventLog:882] - ============================================================
2025-07-25 10:35:17 - INFO - [getWindowsEventLog:883] - 開始獲取 Windows 事件日誌: 172.20.160.33
2025-07-25 10:35:17 - INFO - [getWindowsEventLog:884] - 用戶名: HSWBI00\PowerUser
2025-07-25 10:35:17 - INFO - [getWindowsEventLog:885] - ============================================================
2025-07-25 10:35:17 - INFO - [getWindowsEventLog:891] - 步驟 1: 建立 WMI 連接
2025-07-25 10:35:17 - INFO - [getWindowsEventLog:896] - WMI 連接成功 (耗時: 0.01秒)
2025-07-25 10:36:56 - DEBUG - [_new_conn:1007] - Starting new HTTPS connection (1): api.heysong.dev:443
2025-07-25 10:38:11 - INFO - [getComputerSystemInfo:183] - ============================================================
2025-07-25 10:38:11 - INFO - [getComputerSystemInfo:184] - 開始獲取電腦系統資訊: 172.20.160.33
2025-07-25 10:38:11 - INFO - [getComputerSystemInfo:185] - 用戶名: HSWBI00\PowerUser
2025-07-25 10:38:11 - INFO - [getComputerSystemInfo:186] - ============================================================
2025-07-25 10:38:11 - INFO - [getComputerSystemInfo:192] - 步驟 1: 建立 WMI 連接並測試
2025-07-25 10:38:11 - INFO - [test_wmi_connectivity:66] - 開始測試 WMI 連接: 172.20.160.33 (用戶: HSWBI00\PowerUser)
2025-07-25 10:38:11 - INFO - [test_wmi_connectivity:83] - 嘗試建立 WMI 連接: 172.20.160.33
2025-07-25 10:38:29 - INFO - [test_wmi_connectivity:89] - WMI 連接成功: 172.20.160.33 (耗時: 18.21秒)
2025-07-25 10:38:29 - INFO - [test_wmi_connectivity:94] - WMI 查詢測試成功: 172.20.160.33
2025-07-25 10:38:29 - INFO - [getComputerSystemInfo:202] - WMI 連接建立成功: 172.20.160.33
2025-07-25 10:38:29 - INFO - [getComputerSystemInfo:205] - 步驟 3: 查詢作業系統資訊
2025-07-25 10:38:30 - INFO - [getComputerSystemInfo:212] - Win32_OperatingSystem 查詢完成 (耗時: 0.07秒)
2025-07-25 10:38:30 - INFO - [getComputerSystemInfo:220] - 找到 1 個作業系統記錄
2025-07-25 10:38:30 - INFO - [getComputerSystemInfo:230] - 處理第 1 個作業系統記錄
2025-07-25 10:38:30 - DEBUG - [getComputerSystemInfo:236] - 原始數據 - CSName: HSWBI00
2025-07-25 10:38:30 - DEBUG - [getComputerSystemInfo:237] - 原始數據 - Caption: Microsoft Windows Server 2008 R2 Standard 
2025-07-25 10:38:30 - DEBUG - [getComputerSystemInfo:238] - 原始數據 - BuildNumber: 7601
2025-07-25 10:38:30 - DEBUG - [getComputerSystemInfo:239] - 原始數據 - TotalVisibleMemorySize: 25165368
2025-07-25 10:38:30 - DEBUG - [getComputerSystemInfo:240] - 原始數據 - FreePhysicalMemory: 13107852
2025-07-25 10:38:30 - INFO - [getComputerSystemInfo:266] - 記憶體資訊處理完成 - 總計: 24.00GB, 已使用: 11.50GB, 使用率: 47.91%
2025-07-25 10:38:30 - INFO - [getComputerSystemInfo:276] - 步驟 4: 資料處理完成
2025-07-25 10:38:30 - INFO - [getComputerSystemInfo:277] - 成功處理 1 筆系統資訊
2025-07-25 10:38:30 - INFO - [getComputerSystemInfo:281] - getComputerSystemInfo 完成: 172.20.160.33
2025-07-25 10:38:30 - INFO - [getComputerSystemInfo:282] - ============================================================
2025-07-25 10:38:30 - DEBUG - [_new_conn:1007] - Starting new HTTPS connection (1): api.heysong.dev:443
2025-07-25 10:38:37 - DEBUG - [_make_request:465] - https://api.heysong.dev:443 "POST /api/main_se/CreateComputerInfo/ HTTP/1.1" 200 63
2025-07-25 10:38:37 - INFO - [getDiskInfo:690] - ============================================================
2025-07-25 10:38:37 - INFO - [getDiskInfo:691] - 開始獲取硬碟資訊: 172.20.160.33
2025-07-25 10:38:37 - INFO - [getDiskInfo:692] - 用戶名: HSWBI00\PowerUser
2025-07-25 10:38:37 - INFO - [getDiskInfo:693] - ============================================================
2025-07-25 10:38:37 - INFO - [getDiskInfo:696] - 步驟 1: 建立 CIMv2 WMI 連接
2025-07-25 10:38:37 - INFO - [getDiskInfo:701] - CIMv2 WMI 連接成功 (耗時: 0.02秒)
2025-07-25 10:38:37 - INFO - [getDiskInfo:704] - 步驟 2: 嘗試連接 Storage namespace
2025-07-25 10:38:37 - WARNING - [getDiskInfo:711] - Storage namespace 不可用 (舊系統): <x_wmi: Unexpected COM Error (-2147024891, '存取被拒。', None, None)>
2025-07-25 10:38:37 - DEBUG - [_new_conn:1007] - Starting new HTTPS connection (1): api.heysong.dev:443
2025-07-25 10:38:37 - DEBUG - [_make_request:465] - https://api.heysong.dev:443 "POST /api/main_se/GetDiskInfo/ HTTP/1.1" 200 706
2025-07-25 10:38:37 - DEBUG - [_new_conn:1007] - Starting new HTTPS connection (1): api.heysong.dev:443
2025-07-25 10:38:37 - DEBUG - [_make_request:465] - https://api.heysong.dev:443 "POST /api/main_se/CreateDiskInfo/ HTTP/1.1" 200 63
2025-07-25 10:38:37 - INFO - [getWindowsEventLog:882] - ============================================================
2025-07-25 10:38:37 - INFO - [getWindowsEventLog:883] - 開始獲取 Windows 事件日誌: 172.20.160.33
2025-07-25 10:38:37 - INFO - [getWindowsEventLog:884] - 用戶名: HSWBI00\PowerUser
2025-07-25 10:38:37 - INFO - [getWindowsEventLog:885] - ============================================================
2025-07-25 10:38:37 - INFO - [getWindowsEventLog:891] - 步驟 1: 建立 WMI 連接
2025-07-25 10:38:37 - INFO - [getWindowsEventLog:896] - WMI 連接成功 (耗時: 0.01秒)
2025-07-25 10:38:37 - INFO - [getWindowsEventLog:915] - 步驟 2: 設定查詢時間範圍
2025-07-25 10:38:37 - INFO - [getWindowsEventLog:919] - 查詢時間範圍: 2025-07-22 00:00:00 到 2025-07-25 00:00:00
2025-07-25 10:38:37 - INFO - [getWindowsEventLog:923] - WMI 查詢語句: Select * from Win32_NTLogEvent where (Type = 'Error' OR Type = 'Warning') and TimeGenerated >= '20250722000000.000000-000'
2025-07-25 10:38:37 - INFO - [getWindowsEventLog:926] - 步驟 3: 執行事件日誌查詢
