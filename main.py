import datetime
import json
import json as JSON
import os
import requests as requests

from ComputerHardware import WindowsListen

# import ssl

from apis.api import CreateComputerInfo, CreateDiskInfo, CreateDiskInfoWindowsEventLog
from apis.constant import http_https, localhost, localport
from setting import servers
from tools import service_instance, cli
# from vmDataCenterHardware import vm_dataCenter

# ssl._create_default_https_context = ssl._create_unverified_context

# from pyVmomi import vmodl

servers2 = [
]


def write_to_log_file(file_path, text):
    with open(file_path, 'a', encoding='utf-8') as log_file:
        log_file.write(text + '\n')


if __name__ == '__main__':

    WinDis = WindowsListen()

    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    log_file_path = os.path.join(BASE_DIR, "log.txt")

    for server, username, password in servers:
        try:
            # 電腦資訊
            DataList = JSON.loads(WinDis.getComputerSystemInfo(server, username, password))
            print("電腦資訊獲取成功:")
            print(json.dumps(DataList, ensure_ascii=False))
            write_to_log_file(log_file_path, json.dumps(DataList, ensure_ascii=False))

            url = http_https() + '://' + localhost() + ':' + localport() + CreateComputerInfo()
            headers = {'Content-type': 'application/json'}
            x = requests.post(url, data=json.dumps(DataList), headers=headers)
            print(f"電腦資訊上傳狀態: {x.status_code}")

            # 硬碟資訊
            DataList = JSON.loads(WinDis.getDiskInfo(server, username, password))
            print("硬碟資訊:")
            print(json.dumps(DataList, ensure_ascii=False))

            if DataList["Code"] == "0" and len(DataList["Data"]) > 0:
                url = http_https() + '://' + localhost() + ':' + localport() + CreateDiskInfo()
                headers = {'Content-type': 'application/json'}
                x = requests.post(url, data=json.dumps(DataList), headers=headers)
                print(f"硬碟資訊上傳狀態: {x.status_code}")
            else:
                print("硬碟資訊獲取失敗或無資料，跳過上傳")

            # 電腦日誌
            DataList = JSON.loads(WinDis.getWindowsEventLog(server, username, password))
            print("電腦日誌:")
            print(json.dumps(DataList, ensure_ascii=False))

            if DataList["Code"] == "0":
                url = http_https() + '://' + localhost() + ':' + localport() + CreateDiskInfoWindowsEventLog()
                headers = {'Content-type': 'application/json'}
                x = requests.post(url, data=json.dumps(DataList), headers=headers)
                print(f"電腦日誌上傳狀態: {x.status_code}")
            else:
                print("電腦日誌獲取失敗，跳過上傳")

        except Exception as e:
            error_msg = f"處理伺服器 {server} 時發生錯誤: {str(e)}"
            print(error_msg)
            write_to_log_file(log_file_path, error_msg)

    # 收集所有伺服器資訊
    WinDis.collectServerInfo(alert_threshold=20)

    # 最後一次性發送所有伺服器資訊
    WinDis.sendAllServerInfo(alert_threshold=20)

    # for server, username, password, port in servers2:
    #     try:
    #         si = service_instance.connect(server, username, password, port)
    #
    #         # Do the actual parsing of data
    #         vm_dataCenter(si)
    #
    #     except vmodl.MethodFault as ex:
    #         print("Caught vmodl fault : {}".format(ex.msg))