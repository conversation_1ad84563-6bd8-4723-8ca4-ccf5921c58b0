@echo off
chcp 65001 >nul 2>&1
title 最佳化事件日誌測試

echo ========================================
echo 最佳化 Windows 事件日誌效能測試
echo ========================================
echo.

REM 檢查 Python 是否存在
where python >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python not found in PATH
    echo Please install Python or add it to your PATH
    pause
    exit /b 1
)

REM 設定 Python 執行檔路徑
set PYTHON_EXE=C:\Users\<USER>\PycharmProjects\computerInformation\Src\Scripts\python.exe

REM 檢查腳本檔案是否存在
if not exist "test_optimized_event_log.py" (
    echo Error: test_optimized_event_log.py not found
    echo Please make sure the script is in the current directory
    pause
    exit /b 1
)

REM 執行測試
echo Running optimized Windows Event Log test...
echo.
%PYTHON_EXE% test_optimized_event_log.py

echo.
echo ========================================
echo 測試完成
echo ========================================
echo.
echo 最佳化說明:
echo 1. 查詢時間範圍從1天縮短為12小時
echo 2. 限制最大記錄數為100筆
echo 3. 只查詢重要的日誌來源 (Application, System)
echo 4. 增加詳細的效能監控和進度顯示
echo 5. 改善錯誤處理和異常恢復
echo.
pause
