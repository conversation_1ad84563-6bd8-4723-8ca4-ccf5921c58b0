import cx_Oracle
import pythoncom
import win32com.client
import logging
import os
import uuid
import shutil
import time
import sys
from datetime import datetime

# 取得程式目錄路徑
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# 設定日誌檔案路徑在專案目錄下
log_file_path = os.path.join(BASE_DIR, 'word_process.log')
logging.basicConfig(filename=log_file_path, level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')


# 配置 Oracle 資料庫連接
def get_oracle_connection():
    try:
        oracle_dsn = cx_Oracle.makedsn("172.20.160.32", 1521, service_name="hy1")
        return cx_Oracle.connect(user="asuser", password="asuser", dsn=oracle_dsn)
    except Exception as e:
        logging.error(f"資料庫連接失敗: {str(e)}")
        raise


# 今天的日期
def get_today():
    import datetime
    return datetime.datetime.now().strftime('%Y%m%d')


# 英文星期轉中文星期
def convert_weekday_to_chinese(english_weekday):
    weekday_map = {
        'MONDAY': '星期一',
        'TUESDAY': '星期二',
        'WEDNESDAY': '星期三',
        'THURSDAY': '星期四',
        'FRIDAY': '星期五',
        'SATURDAY': '星期六',
        'SUNDAY': '星期日'
    }
    # 去除可能的空格並轉大寫比對
    clean_weekday = english_weekday.strip().upper()
    return weekday_map.get(clean_weekday, english_weekday)


def fetch_disk_space_from_db():
    query = """
            SELECT 'YYY', TO_CHAR(TO_CHAR(SYSDATE, 'YYYY') - 1911) AS YEAR, '', '', '' \
            FROM DUAL
            UNION ALL
            SELECT 'MM', TO_CHAR(SYSDATE, 'MM') AS MONTH, '', '', '' \
            FROM DUAL
            UNION ALL
            SELECT 'DAY', TO_CHAR(SYSDATE, 'DD') AS DAY, '', '', '' \
            FROM DUAL
            UNION ALL
            SELECT 'WEEKDAY', TO_CHAR(SYSDATE, 'DAY') AS DAYOFWEEK, '', '', '' \
            FROM DUAL
            UNION ALL
            SELECT REPLACE(AADNNA2 || ABCANM, ':', ''), \
                   TO_CHAR(TRUNC(ABUSSE, 2), 'FM9999999990.00') || REPLACE(ABDKUT, 'B', ''), \
                   AADNNA, \
                   AANAME, \
                   ABCANM
            FROM SEAB, \
                 SEAA_M M
            WHERE ABDNNA = M.AADNNA \
              AND ABDATE = :qDATE \
            """

    # get_today()今天的日期西元年轉民國年
    year = str(int(get_today()[:4]) - 1911)
    month = get_today()[4:6]
    day = get_today()[6:]

    condition = {
        "qDATE": f"{year}{month}{day}"
    }

    cursor = get_oracle_connection().cursor()
    cursor.execute(query, condition)
    data = cursor.fetchall()

    result = {}
    for row in data:
        key = row[0]
        value = row[1]

        # 特別處理星期幾的情況
        if key == 'WEEKDAY':
            value = convert_weekday_to_chinese(value)

        result[key] = value

    return result


def format_replacement(paragraph_or_cell_range, pattern, replacement_text):
    try:
        search_range = paragraph_or_cell_range.Duplicate
        search_range.Find.Text = pattern
        search_range.Find.Execute()
        while search_range.Find.Found:
            search_range.Text = replacement_text
            search_range.Font.Name = "新細明體"
            search_range.Font.Size = 12
            search_range.Font.Bold = True
            search_range.Collapse(Direction=2)
            search_range.Find.Text = pattern
            search_range.Find.Execute()
    except Exception as e:
        logging.error(f"格式替換錯誤: {str(e)}")


def replace_and_format(paragraph_or_cell, notes_data):
    try:
        if hasattr(paragraph_or_cell, 'Range'):
            text_content = paragraph_or_cell.Range.Text
            target_range = paragraph_or_cell.Range
        else:
            return

        for key, value in notes_data.items():
            pattern = f'{key}'
            try:
                if pattern in text_content:
                    format_replacement(target_range, pattern, value)
                    logging.info(f"已替換 {pattern} 為 {value}")
            except Exception as e:
                logging.error(f"替換格式時發生錯誤: {str(e)}")
    except Exception as e:
        logging.error(f"替換及格式化錯誤: {str(e)}")


def setup_word2010_options(word_app):
    """設定 Word 2010 特定選項"""
    try:
        # 禁用顯示警告
        word_app.DisplayAlerts = False

        # 禁用自動語法檢查
        word_app.Options.CheckGrammarAsYouType = False
        word_app.Options.CheckSpellingAsYouType = False

        # 禁用轉換確認
        word_app.Options.ConfirmConversions = False

        # 禁用文件信息面板
        try:
            word_app.Options.EnableLivePreview = False
        except:
            pass

        # 禁用受保護視圖
        try:
            word_app.Options.Security.EnableProtectedView = False
        except:
            pass

        logging.info("已設定 Word 2010 特定選項")
    except Exception as e:
        logging.error(f"設定 Word 選項時發生錯誤: {str(e)}")


def setup_word2010_registry():
    """設定 Word 2010 的註冊表項目以禁用受保護視圖"""
    try:
        import winreg

        # Word 2010 的註冊表位置
        key_path = r"Software\Microsoft\Office\14.0\Word\Security"

        try:
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_WRITE)
        except:
            key = winreg.CreateKey(winreg.HKEY_CURRENT_USER, key_path)

        # 設置安全層級 (1 = 低)
        winreg.SetValueEx(key, "VBAWarnings", 0, winreg.REG_DWORD, 1)

        # 受保護視圖路徑
        pv_path = r"Software\Microsoft\Office\14.0\Word\Security\ProtectedView"
        try:
            pv_key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, pv_path, 0, winreg.KEY_WRITE)
        except:
            pv_key = winreg.CreateKey(winreg.HKEY_CURRENT_USER, pv_path)

        # 關閉所有受保護視圖選項
        winreg.SetValueEx(pv_key, "DisableAttachmentsInPV", 0, winreg.REG_DWORD, 1)
        winreg.SetValueEx(pv_key, "DisableInternetFilesInPV", 0, winreg.REG_DWORD, 1)
        winreg.SetValueEx(pv_key, "DisableUnsafeLocationsInPV", 0, winreg.REG_DWORD, 1)

        # 關閉註冊表鍵
        winreg.CloseKey(pv_key)
        winreg.CloseKey(key)

        logging.info("已設定 Word 2010 註冊表項目")
    except Exception as e:
        logging.error(f"設定註冊表時發生錯誤: {str(e)}")


def modify_file_content(user_id, pudcno, file_path, file_name, word_app):
    doc = None
    new_file_path = ""
    new_file_name = ""

    try:
        notes_data = fetch_disk_space_from_db()
        if not notes_data:
            logging.warning("從資料庫獲取的資料為空")

        # 確保文件存在
        if not os.path.exists(file_path):
            logging.error(f"文件不存在: {file_path}")
            return file_path, file_name

        # 建立備份
        backup_path = f"{file_path}.bak"
        try:
            shutil.copy2(file_path, backup_path)
            logging.info(f"已建立備份: {backup_path}")
        except Exception as e:
            logging.warning(f"建立備份失敗: {str(e)}")

        # 確保輸出目錄存在
        output_dir = os.path.join(BASE_DIR, "電腦室日誌")
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 設定新文件名和路徑
        base_name, ext = os.path.splitext(file_name)
        new_file_name = f"{base_name}_{get_today()}{ext}"
        new_file_path = os.path.join(output_dir, new_file_name)

        # 確保沒有同名文件
        if os.path.exists(new_file_path):
            try:
                os.remove(new_file_path)
            except:
                new_file_name = f"{base_name}_{get_today()}_{uuid.uuid4().hex[:4]}{ext}"
                new_file_path = os.path.join(output_dir, new_file_name)

        # 設定 Word 選項
        setup_word2010_options(word_app)

        # 嘗試解決鎖定問題：先複製文件
        temp_file_path = os.path.join(BASE_DIR, f"temp_{uuid.uuid4().hex[:8]}.docx")
        shutil.copy2(file_path, temp_file_path)

        # 開啟文件 - Word 2010 特有的方式
        try:
            logging.info(f"嘗試開啟臨時文件: {temp_file_path}")
            doc = word_app.Documents.Open(
                FileName=temp_file_path,
                ReadOnly=False,
                Visible=False,
                ConfirmConversions=False
            )
            logging.info("成功開啟文件")
        except Exception as e:
            logging.error(f"開啟臨時文件失敗: {str(e)}")

            # 如果失敗，嘗試直接開啟原始文件
            try:
                logging.info(f"嘗試開啟原始文件: {file_path}")
                doc = word_app.Documents.Open(file_path)
                logging.info("成功開啟原始文件")
            except Exception as e2:
                logging.error(f"開啟原始文件也失敗: {str(e2)}")

                # 嘗試清理並再次嘗試
                try:
                    os.remove(temp_file_path)
                    time.sleep(2)
                    kill_word_processes()
                    time.sleep(2)
                    doc = word_app.Documents.Open(file_path, ReadOnly=True)
                    logging.info("成功以唯讀方式開啟文件")
                except Exception as e3:
                    logging.error(f"所有開啟嘗試都失敗: {str(e3)}")
                    return file_path, file_name

        # 檢查文件是否成功開啟
        if not doc:
            logging.error("文件未能成功開啟")
            return file_path, file_name

        # 替換內容
        for paragraph in doc.Paragraphs:
            replace_and_format(paragraph, notes_data)

        for table in doc.Tables:
            for row in table.Rows:
                for cell in row.Cells:
                    replace_and_format(cell.Range, notes_data)

        # 保存為新文件
        try:
            logging.info(f"嘗試保存文件為: {new_file_path}")
            doc.SaveAs(new_file_path)
            logging.info("文件已保存")
        except Exception as e:
            logging.error(f"保存文件失敗: {str(e)}")

            # 嘗試另一種保存方式
            try:
                doc.SaveAs2(FileName=new_file_path)
                logging.info("使用 SaveAs2 保存文件成功")
            except Exception as e2:
                logging.error(f"使用 SaveAs2 保存文件也失敗: {str(e2)}")
                return file_path, file_name

        # 關閉文件
        try:
            doc.Close(SaveChanges=False)  # 已經保存過了
            doc = None
            logging.info("文件已關閉")
        except Exception as e:
            logging.error(f"關閉文件時發生錯誤: {str(e)}")
            doc = None

        # 清理臨時文件
        try:
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
                logging.info("臨時文件已刪除")
        except:
            pass

        logging.info(f"文件已處理並保存為: {new_file_path}")
        return new_file_path, new_file_name
    except Exception as e:
        logging.error(f"修改文件內容時發生錯誤: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return file_path, file_name
    finally:
        # 確保文件被關閉
        if doc:
            try:
                doc.Close(SaveChanges=False)
                doc = None
            except:
                doc = None

        # 清理臨時文件
        try:
            if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
                os.remove(temp_file_path)
        except:
            pass


def kill_word_processes():
    """終止所有Word進程，確保不會有殘留的Word實例"""
    try:
        import subprocess
        code = subprocess.call('tasklist | findstr WINWORD.EXE', shell=True)
        if code == 0:
            subprocess.call('taskkill /f /im WINWORD.EXE', shell=True)
            time.sleep(1)
            logging.info("已終止所有Word進程")
        else:
            logging.info("系統中沒有Word進程，不需終止。")
    except Exception as e:
        logging.error(f"終止Word進程失敗: {str(e)}")


def process_word_document(file_path, file_name, pudcno, user_id, file_password=None):
    # 前置清理，確保沒有殘留的Word進程
    kill_word_processes()

    word_app = None
    try:
        # 設定註冊表項目，禁用受保護視圖
        setup_word2010_registry()

        # 初始化COM
        pythoncom.CoInitialize()

        # 創建Word應用 - 使用Dispatch而非DispatchEx
        word_app = win32com.client.Dispatch("Word.Application")
        word_app.Visible = False

        # 設定Word選項
        setup_word2010_options(word_app)

        logging.info(f"開始處理文件: {file_path}")

        # 處理文件
        if file_password:
            result = process_protected_document(word_app, file_path, file_name, pudcno, user_id, file_password)
        else:
            result = modify_file_content(user_id, pudcno, file_path, file_name, word_app)

        # 關閉Word應用
        try:
            word_app.Quit()
            word_app = None
            logging.info("Word應用已正常關閉")
        except Exception as e:
            logging.error(f"關閉Word應用時發生錯誤: {str(e)}")
            word_app = None

        # 清理COM
        pythoncom.CoUninitialize()

        return result
    except Exception as e:
        logging.error(f"處理Word文件時發生錯誤: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return file_path, file_name
    finally:
        # 確保Word應用程式被關閉
        if word_app:
            try:
                word_app.Quit()
            except:
                pass
            word_app = None

        try:
            pythoncom.CoUninitialize()
        except:
            pass

        # 確保所有Word進程都已終止
        kill_word_processes()


def process_protected_document(word_app, file_path, file_name, pudcno, user_id, file_password):
    doc = None
    temp_files = []

    try:
        temp_uuid = str(uuid.uuid4())
        # 創建臨時文件在專案目錄下
        temp_dir = os.path.join(BASE_DIR, "temp")
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)

        unprotected_path = os.path.join(temp_dir, f"{os.path.basename(file_path)}.{temp_uuid}.unprotected.docx")
        temp_files.append(unprotected_path)

        # Word 2010 開啟受保護文件
        try:
            doc = word_app.Documents.Open(
                FileName=file_path,
                PasswordDocument=file_password,
                ReadOnly=False,
                Visible=False,
                ConfirmConversions=False
            )
        except Exception as e:
            logging.error(f"開啟受保護文件失敗: {str(e)}")

            # 簡單方式嘗試
            doc = word_app.Documents.Open(file_path, PasswordDocument=file_password)

        # 解除保護
        if doc.ProtectionType != -1:
            doc.Unprotect(Password=file_password)

        # 保存解除保護的文件
        doc.SaveAs(unprotected_path)
        doc.Close(SaveChanges=False)
        doc = None

        # 修改文件內容
        new_path, new_name = modify_file_content(user_id, pudcno, unprotected_path, file_name, word_app)

        # 重新保護文件
        doc = word_app.Documents.Open(new_path)
        if doc.ProtectionType == -1:
            doc.Protect(Type=3, Password=file_password)

        # 保存並關閉
        doc.Save()
        doc.Close(SaveChanges=False)
        doc = None

        return new_path, new_name
    except Exception as e:
        logging.error(f"處理受保護文件時發生錯誤: {str(e)}")
        return file_path, file_name
    finally:
        # 確保文件被關閉
        if doc:
            try:
                doc.Close(SaveChanges=False)
                doc = None
            except:
                doc = None

        # 清理臨時文件
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass


def unprotect_and_protect_docx(file_path, file_name, pudcno, user_id, file_password, modify_func):
    """原始的開啟Word檔案並處理的函數，保留以確保兼容性"""
    pythoncom.CoInitialize()

    # 前置清理，確保沒有殘留的Word進程
    kill_word_processes()

    # 設定註冊表以禁用受保護視圖
    setup_word2010_registry()

    word_app = None
    try:
        # 創建Word應用
        word_app = win32com.client.Dispatch("Word.Application")
        word_app.Visible = False

        # 設定Word選項，減少對話框彈出
        setup_word2010_options(word_app)

        if file_password is None:
            try:
                new_path, new_name = modify_func(user_id, pudcno, file_path, file_name, word_app)
                return new_path, new_name
            except Exception as e:
                logging.error("Error: " + str(e))
                import traceback
                logging.error(traceback.format_exc())
                return file_path, file_name
        else:
            try:
                # 開啟受保護文件
                doc = word_app.Documents.Open(FileName=file_path, PasswordDocument=file_password)
                if doc.ProtectionType != -1:
                    doc.Unprotect(Password=file_password)

                temp_uuid = str(uuid.uuid4())
                unprotected_path = f"{file_path}.{temp_uuid}.unprotected.docx"
                doc.SaveAs2(FileName=unprotected_path, FileFormat=16)
                doc.Close()

                new_path, new_name = modify_func(user_id, pudcno, unprotected_path, file_name, word_app)

                doc = word_app.Documents.Open(FileName=new_path)
                if doc.ProtectionType == -1:
                    doc.Protect(Type=3, Password=file_password)
                protected_new_path = f"{new_path}.{temp_uuid}.protected.docx"
                doc.SaveAs2(FileName=protected_new_path, FileFormat=16)
                doc.Close()

                if os.path.exists(new_path):
                    os.remove(new_path)
                if os.path.exists(unprotected_path):
                    os.remove(unprotected_path)

                os.rename(protected_new_path, new_path)
                return new_path, new_name
            except Exception as e:
                logging.error("Error: " + str(e))
                import traceback
                logging.error(traceback.format_exc())
                return file_path, file_name
    finally:
        # 確保Word應用程式被關閉
        if word_app:
            try:
                word_app.Quit()
            except:
                pass
            word_app = None

        try:
            pythoncom.CoUninitialize()
        except:
            pass

        # 確保所有Word進程都已終止
        kill_word_processes()


def main():
    try:
        BASE_DIR = os.path.dirname(os.path.abspath(__file__))
        file_url = os.path.join(BASE_DIR, "電腦室日誌.docx")
        file_name = "電腦室日誌.docx"

        if not os.path.exists(file_url):
            logging.error(f"找不到源文件: {file_url}")
            print(f"錯誤: 找不到源文件 {file_url}")
            return None, None

        logging.info(f"找到Word文件: {file_url}")
        print(f"找到Word文件: {file_url}")

        user_id = "16613"
        pudcno = "123456"
        file_password = None

        # 請將 unprotect_and_protect_docx 與 modify_file_content、其餘函式內容維持原樣
        new_path, new_name = unprotect_and_protect_docx(
            file_url, file_name, pudcno, user_id, file_password, modify_file_content
        )
        logging.info(f"處理完成，新文件路徑: {new_path}, 新文件名稱: {new_name}")
        print(f"文件處理完成: {new_path}")

        return new_path, new_name
    except Exception as e:
        logging.error(f"主程序發生錯誤: {str(e)}")
        print(f"處理文件時發生錯誤: {str(e)}")
        return None, None

if __name__ == "__main__":
    try:
        main()
    finally:
        kill_word_processes()
