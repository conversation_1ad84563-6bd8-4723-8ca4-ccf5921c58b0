#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試最佳化後的 Windows 事件日誌功能
專門針對 HSWDC00 效能問題進行測試
"""

import sys
import os
import json
import time
from datetime import datetime

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ComputerHardware import WindowsListen
from setting import servers

def test_optimized_event_log():
    """測試最佳化後的事件日誌功能"""
    print(f"最佳化 Windows 事件日誌測試")
    print(f"開始時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"=" * 80)
    
    if not servers:
        print(f"❌ 沒有找到要測試的伺服器配置")
        return
    
    win_listener = WindowsListen()
    
    for i, (ip, username, password) in enumerate(servers, 1):
        print(f"\n測試伺服器 {i}: {ip}")
        print(f"用戶名: {username}")
        print(f"-" * 60)
        
        try:
            # 記錄總開始時間
            total_start_time = time.time()
            
            print(f"正在獲取事件日誌...")
            result = win_listener.getWindowsEventLog(ip, username, password)
            
            total_elapsed_time = time.time() - total_start_time
            
            # 解析結果
            data = json.loads(result)
            
            print(f"\n結果摘要:")
            print(f"返回代碼: {data['Code']}")
            print(f"返回訊息: {data['Msg']}")
            print(f"總耗時: {total_elapsed_time:.2f}秒")
            
            if data["Code"] == "0":
                print(f"✅ 事件日誌獲取成功")
                print(f"日誌筆數: {len(data['Data'])}")
                
                # 分析日誌類型分布
                if data['Data']:
                    type_count = {}
                    logfile_count = {}
                    
                    for log_entry in data['Data']:
                        log_type = log_entry.get('Type', '未知')
                        logfile = log_entry.get('Logfile', '未知')
                        
                        type_count[log_type] = type_count.get(log_type, 0) + 1
                        logfile_count[logfile] = logfile_count.get(logfile, 0) + 1
                    
                    print(f"\n日誌類型分布:")
                    for log_type, count in type_count.items():
                        print(f"  {log_type}: {count} 筆")
                    
                    print(f"\n日誌來源分布:")
                    for logfile, count in logfile_count.items():
                        print(f"  {logfile}: {count} 筆")
                    
                    print(f"\n最新5筆日誌:")
                    # 按時間排序，顯示最新的5筆
                    sorted_logs = sorted(data['Data'], 
                                       key=lambda x: (x.get('TimeGenerated_Date', ''), 
                                                     x.get('TimeGenerated_Time', '')), 
                                       reverse=True)
                    
                    for j, log_entry in enumerate(sorted_logs[:5], 1):
                        date = log_entry.get('TimeGenerated_Date', 'N/A')
                        time_str = log_entry.get('TimeGenerated_Time', 'N/A')
                        log_type = log_entry.get('Type', 'N/A')
                        message = log_entry.get('Message', 'N/A')[:80]
                        
                        print(f"  {j}. [{date} {time_str}] {log_type} - {message}...")
                else:
                    print(f"  沒有找到符合條件的日誌")
                    
                # 效能評估
                print(f"\n效能評估:")
                if total_elapsed_time < 30:
                    print(f"✅ 效能良好 (< 30秒)")
                elif total_elapsed_time < 60:
                    print(f"⚠️  效能一般 (30-60秒)")
                else:
                    print(f"❌ 效能較差 (> 60秒)")
                    
            else:
                print(f"❌ 事件日誌獲取失敗")
                print(f"錯誤詳情: {data['Msg']}")
                
        except Exception as e:
            print(f"❌ 測試異常: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n" + "=" * 80)
    print(f"測試完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"\n最佳化重點:")
    print(f"1. 查詢時間範圍縮短為12小時")
    print(f"2. 限制查詢結果最多100筆記錄")
    print(f"3. 只查詢 Application 和 System 日誌")
    print(f"4. 增加詳細的效能監控")
    print(f"5. 改善錯誤處理機制")

if __name__ == "__main__":
    test_optimized_event_log()
