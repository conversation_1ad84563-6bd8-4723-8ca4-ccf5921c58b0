#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 Windows 事件日誌功能
"""

import sys
import os
import json
from datetime import datetime

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ComputerHardware import WindowsListen
from setting import servers

def test_event_log():
    """測試事件日誌功能"""
    print(f"Windows 事件日誌測試")
    print(f"開始時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"=" * 60)
    
    if not servers:
        print(f"❌ 沒有找到要測試的伺服器配置")
        return
    
    win_listener = WindowsListen()
    
    for i, (ip, username, password) in enumerate(servers, 1):
        print(f"\n測試伺服器 {i}: {ip}")
        print(f"用戶名: {username}")
        print(f"-" * 40)
        
        try:
            # 測試事件日誌
            print(f"正在獲取事件日誌...")
            result = win_listener.getWindowsEventLog(ip, username, password)
            
            # 解析結果
            data = json.loads(result)
            
            print(f"返回代碼: {data['Code']}")
            print(f"返回訊息: {data['Msg']}")
            
            if data["Code"] == "0":
                print(f"✅ 事件日誌獲取成功")
                print(f"日誌筆數: {len(data['Data'])}")
                
                # 顯示前幾筆日誌
                if data['Data']:
                    print(f"\n前5筆日誌:")
                    for j, log_entry in enumerate(data['Data'][:5], 1):
                        print(f"  {j}. [{log_entry.get('TimeGenerated_Date', 'N/A')} {log_entry.get('TimeGenerated_Time', 'N/A')}] "
                              f"{log_entry.get('Type', 'N/A')} - {log_entry.get('Message', 'N/A')[:50]}...")
                else:
                    print(f"  沒有找到符合條件的日誌")
            else:
                print(f"❌ 事件日誌獲取失敗")
                print(f"錯誤詳情: {data['Msg']}")
                
        except Exception as e:
            print(f"❌ 測試異常: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n" + "=" * 60)
    print(f"測試完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    test_event_log()
