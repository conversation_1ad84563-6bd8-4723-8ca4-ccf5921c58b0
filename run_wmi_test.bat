@echo off
chcp 65001 >nul 2>&1
echo ========================================
echo WMI Connection Diagnostic Test
echo ========================================
echo.

REM 設置環境變數
set PYTHONIOENCODING=utf-8

REM 檢查 Python 是否存在
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found in PATH
    echo Please make sure Python is installed and added to PATH
    pause
    exit /b 1
)

REM 檢查虛擬環境
if exist "Src\Scripts\python.exe" (
    echo Using virtual environment...
    set PYTHON_EXE=Src\Scripts\python.exe
) else (
    echo Using system Python...
    set PYTHON_EXE=python
)

REM 執行測試
echo Running WMI connection test...
echo.
%PYTHON_EXE% test_wmi_connection.py

echo.
echo ========================================
echo Test completed. Check wmi_connection_debug.log for details.
echo ========================================
pause
