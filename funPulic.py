# 取得值
import datetime


# 當天日期
def today_Date():
    today = datetime.date.today()
    return (str(today.year - 1911) + str(today.month).zfill(2) + str(today.day).zfill(2))


# 前七天日期
def before_7_Date():
    today = datetime.date.today()
    b_7_Date = today + datetime.timedelta(days=-7)
    return (str(b_7_Date.year - 1911) + str(b_7_Date.month).zfill(2) + str(b_7_Date.day).zfill(2))


# 後七天日期
def after_7_Date():
    today = datetime.date.today()
    a_7_Date = today + datetime.timedelta(days=7)
    return (str(a_7_Date.year - 1911) + str(a_7_Date.month).zfill(2) + str(a_7_Date.day).zfill(2))


# 前三天日期
def before_bc_3_Date():
    today = datetime.date.today()
    a_3_Date = today + datetime.timedelta(days=-3)
    return (str(a_3_Date.year) + str(a_3_Date.month).zfill(2) + str(a_3_Date.day).zfill(2))


# 前十天日期
def before_bc_10_Date():
    today = datetime.date.today()
    a_10_Date = today + datetime.timedelta(days=-10)
    return (str(a_10_Date.year) + str(a_10_Date.month).zfill(2) + str(a_10_Date.day).zfill(2))


# 前三十天日期
def before_bc_30_Date():
    today = datetime.date.today()
    a_30_Date = today + datetime.timedelta(days=-30)
    return (str(a_30_Date.year) + str(a_30_Date.month).zfill(2) + str(a_30_Date.day).zfill(2))


# 前九十天日期
def before_bc_90_Date():
    today = datetime.date.today()
    a_90_Date = today + datetime.timedelta(days=-90)
    return (str(a_90_Date.year) + str(a_90_Date.month).zfill(2) + str(a_90_Date.day).zfill(2))


def date2sec(evt_date):
    '''
    This function converts dates with format
    'Thu Jul 13 08:22:34 2017' to seconds since 1970.
    '''
    dt = datetime.datetime.strptime(evt_date, "%a %b %d %H:%M:%S %Y")
    return dt.timestamp()
