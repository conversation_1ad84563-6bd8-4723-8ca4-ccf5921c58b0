#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WMI 連接診斷測試腳本
用於診斷 WMI 連接問題並提供詳細的錯誤分析
"""

import sys
import os
import time
import json
from datetime import datetime

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ComputerHardware import WindowsListen, test_wmi_connectivity, logger
from setting import servers

def test_single_server(ip, username, password):
    """測試單個伺服器的連接"""
    print(f"\n{'='*80}")
    print(f"測試伺服器: {ip}")
    print(f"用戶名: {username}")
    print(f"時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*80}")
    
    # 1. WMI 連接測試
    print(f"\n1. WMI 連接測試")
    print(f"-" * 40)
    try:
        wmi_success, wmi_connection, error_msg = test_wmi_connectivity(ip, username, password, timeout=120)
        print(f"WMI 連接: {'✓ 成功' if wmi_success else '✗ 失敗'}")
        print(f"結果: {error_msg}")

        if not wmi_success:
            print(f"❌ WMI 連接失敗，無法繼續測試")
            return False

    except Exception as e:
        print(f"❌ WMI 測試異常: {e}")
        return False

    # 2. 實際功能測試
    print(f"\n2. 實際功能測試")
    print(f"-" * 40)
    
    win_listener = WindowsListen()
    
    # 測試電腦系統資訊
    try:
        print(f"測試電腦系統資訊...")
        start_time = time.time()
        result = win_listener.getComputerSystemInfo(ip, username, password)
        elapsed_time = time.time() - start_time
        
        data = json.loads(result)
        if data["Code"] == "0":
            print(f"✓ 電腦系統資訊獲取成功 (耗時: {elapsed_time:.2f}秒)")
            print(f"  資料筆數: {len(data['Data'])}")
            if data['Data']:
                first_record = data['Data'][0]
                print(f"  主機名稱: {first_record.get('CSName', 'N/A')}")
                print(f"  作業系統: {first_record.get('Caption', 'N/A')}")
                print(f"  記憶體使用率: {first_record.get('Rate', 'N/A')}")
        else:
            print(f"✗ 電腦系統資訊獲取失敗: {data['Msg']}")
            return False
            
    except Exception as e:
        print(f"❌ 電腦系統資訊測試異常: {e}")
        return False
    
    # 測試硬碟資訊
    try:
        print(f"測試硬碟資訊...")
        start_time = time.time()
        result = win_listener.getDiskInfo(ip, username, password)
        elapsed_time = time.time() - start_time
        
        data = json.loads(result)
        if data["Code"] == "0":
            print(f"✓ 硬碟資訊獲取成功 (耗時: {elapsed_time:.2f}秒)")
            print(f"  硬碟數量: {len(data['Data'])}")
            for disk in data['Data'][:3]:  # 只顯示前3個硬碟
                print(f"  - {disk.get('Caption', 'N/A')}: {disk.get('Rate', 'N/A')} 使用率")
        else:
            print(f"✗ 硬碟資訊獲取失敗: {data['Msg']}")
            
    except Exception as e:
        print(f"❌ 硬碟資訊測試異常: {e}")
    
    # 測試事件日誌
    try:
        print(f"測試事件日誌...")
        start_time = time.time()
        result = win_listener.getWindowsEventLog(ip, username, password)
        elapsed_time = time.time() - start_time
        
        data = json.loads(result)
        if data["Code"] == "0":
            print(f"✓ 事件日誌獲取成功 (耗時: {elapsed_time:.2f}秒)")
            print(f"  日誌筆數: {len(data['Data'])}")
        else:
            print(f"✗ 事件日誌獲取失敗: {data['Msg']}")
            
    except Exception as e:
        print(f"❌ 事件日誌測試異常: {e}")
    
    print(f"\n✅ 伺服器 {ip} 測試完成")
    return True

def main():
    """主函數"""
    print(f"WMI 連接診斷測試工具")
    print(f"開始時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"日誌文件: wmi_connection_debug.log")
    
    if not servers:
        print(f"❌ 沒有找到要測試的伺服器配置")
        return
    
    print(f"\n找到 {len(servers)} 個伺服器配置:")
    for i, (ip, username, password) in enumerate(servers, 1):
        print(f"  {i}. {ip} ({username})")
    
    success_count = 0
    total_count = len(servers)
    
    for ip, username, password in servers:
        try:
            if test_single_server(ip, username, password):
                success_count += 1
        except KeyboardInterrupt:
            print(f"\n⚠️  用戶中斷測試")
            break
        except Exception as e:
            print(f"❌ 測試伺服器 {ip} 時發生未預期錯誤: {e}")
    
    print(f"\n{'='*80}")
    print(f"測試總結")
    print(f"{'='*80}")
    print(f"總伺服器數: {total_count}")
    print(f"成功連接: {success_count}")
    print(f"失敗連接: {total_count - success_count}")
    print(f"成功率: {(success_count/total_count)*100:.1f}%")
    print(f"結束時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"\n詳細日誌請查看: wmi_connection_debug.log")

if __name__ == "__main__":
    main()
