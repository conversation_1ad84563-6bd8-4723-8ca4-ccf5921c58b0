#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 Windows 事件日誌的語言環境
"""

import sys
import os
import wmi
import time
from datetime import datetime, timedelta

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from setting import servers

def check_event_log_language():
    """檢查事件日誌的語言環境"""
    
    if not servers:
        print("沒有找到伺服器配置")
        return
    
    # 只測試第一個伺服器
    ip, username, password = servers[0]
    
    print(f"檢查伺服器: {ip}")
    print(f"用戶名: {username}")
    print(f"=" * 60)
    
    try:
        # 連接 WMI
        print("正在連接 WMI...")
        getData = wmi.WMI(computer=ip, user=username, password=password)
        print("WMI 連接成功")
        
        # 設定查詢時間範圍 - 只查詢最近1小時
        end_date = datetime.now()
        start_date = end_date - timedelta(hours=1)
        
        print(f"查詢時間範圍: {start_date} 到 {end_date}")
        
        # 查詢少量事件來檢查語言
        query = f"Select TimeGenerated, Type, Logfile, Message from Win32_NTLogEvent where TimeGenerated >= '{start_date.strftime('%Y%m%d%H%M%S.000000-000')}'"
        
        print(f"執行查詢: {query}")
        
        events = getData.query(query)
        
        print(f"\n找到 {len(events)} 個事件")

        # 收集所有不同的 Type 值
        type_values = set()

        print(f"\n事件詳情 (只顯示前10個):")
        for i, event in enumerate(events[:10], 1):  # 只處理前10個
            event_type = getattr(event, 'Type', 'N/A')
            logfile = getattr(event, 'Logfile', 'N/A')
            message = getattr(event, 'Message', 'N/A')

            type_values.add(event_type)

            print(f"  {i}. Type: '{event_type}', Logfile: '{logfile}'")
            if message and message != 'N/A':
                print(f"     Message: {message[:100]}...")
            print()
        
        print(f"發現的 Type 值:")
        for type_val in sorted(type_values):
            print(f"  - '{type_val}'")
        
        # 測試不同的查詢條件
        print(f"\n測試不同的查詢條件:")
        
        test_conditions = [
            "(Type = 'Error' OR Type = 'Warning')",
            "(Type = '錯誤' OR Type = '警告')",
            "(Type = 'Information')",
            "(Type = '信息' OR Type = '資訊')"
        ]
        
        for condition in test_conditions:
            try:
                test_query = f"Select COUNT(*) as Count from Win32_NTLogEvent where {condition} and TimeGenerated >= '{start_date.strftime('%Y%m%d%H%M%S.000000-000')}'"
                print(f"  測試: {condition}")
                
                result = getData.query(test_query)
                if result:
                    count = getattr(result[0], 'Count', 0)
                    print(f"    結果: {count} 筆記錄")
                else:
                    print(f"    結果: 0 筆記錄")
                    
            except Exception as e:
                print(f"    錯誤: {e}")
        
    except Exception as e:
        print(f"測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_event_log_language()
